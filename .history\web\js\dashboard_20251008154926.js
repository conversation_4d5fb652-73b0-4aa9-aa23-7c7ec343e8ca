(function(){
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init(){
    if (typeof Chart === 'undefined') return;
    initSalesChart();
    initBrowserUsageChart();
  }

  // function initSalesChart(){
  //   var canvas = document.getElementById('salesChart');
  //   if (!canvas) return;
  //   var ctx = canvas.getContext('2d');
  //   new Chart(ctx, {
  //     type: 'bar',
  //     data: {
  //       labels: ['Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct'],
  //       datasets: [{
  //         label: 'This year',
  //         data: [38, 42, 58, 52, 60, 57, 62, 59, 64],
  //         backgroundColor: '#1e66ff'
  //       },{
  //         label: 'Last year',
  //         data: [28, 36, 33, 27, 35, 38, 41, 45, 37],
  //         backgroundColor: '#adb5bd'
  //       }]
  //     },
  //     options: {
  //       responsive: true,
  //       maintainAspectRatio: false,
  //       plugins: { legend: { display: true, position: 'bottom' } },
  //       scales: {
  //         x: { grid: { display: false } },
  //         y: { beginAtZero: true, suggestedMax: 120 }
  //       }
  //     }
  //   });
  // }

  function initBrowserUsageChart(){
    var canvas = document.getElementById('browserUsageChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Chrome','Edge','FireFox','Safari','Opera','IE'],
        datasets: [{
          data: [27, 20, 16, 24, 11, 2],
          backgroundColor: ['#1e66ff','#20c997','#f59f00','#d6336c','#6741d9','#adb5bd'],
          borderColor: '#fff',
          borderWidth: 2,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        cutout: '65%',
        plugins: {
          legend: {
            position: 'right',
            labels: { usePointStyle: true, pointStyle: 'circle' }
          },
          tooltip: {
            callbacks: {
              label: function(ctx){
                var label = ctx.label || '';
                var val = ctx.parsed;
                var total = ctx.chart._metasets[0].total || ctx.dataset.data.reduce(function(a,b){return a+b;},0);
                var pct = Math.round((val/total)*100);
                return label+': '+val+' ('+pct+'%)';
              }
            }
          }
        }
      }
    });
  }
})();

