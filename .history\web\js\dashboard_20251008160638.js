(function(){
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init(){
    if (typeof Chart === 'undefined') return;
    initSalesChart();
    initBrowserUsageChart();
    initTrafficChart();

  }

  function initSalesChart(){
    var canvas = document.getElementById('salesChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct'],
        datasets: [
          {
            label: 'This year',
            data: [36, 38, 58, 52, 60, 58, 61, 59, 63],
            backgroundColor: '#1e66ff',
            borderRadius: 3,
            categoryPercentage: 0.6,
            barPercentage: 0.8,
            maxBarThickness: 14
          },
          {
            label: 'Last year',
            data: [28, 36, 33, 27, 35, 38, 41, 45, 37],
            backgroundColor: '#adb5bd',
            borderRadius: 3,
            categoryPercentage: 0.6,
            barPercentage: 0.8,
            maxBarThickness: 14
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: true, position: 'bottom', labels: { usePointStyle: true, pointStyle: 'rectRounded' } },
          tooltip: { mode: 'index', intersect: false }
        },
        scales: {
          x: { grid: { display: false } },
          y: {
            beginAtZero: true,
            suggestedMax: 120,
            ticks: { stepSize: 30 },
            grid: { color: 'rgba(0,0,0,0.06)' }
          }
        }
      }
    });
  }

  function initBrowserUsageChart(){
    var canvas = document.getElementById('browserUsageChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Chrome','Edge','FireFox','Safari','Opera','IE'],
        datasets: [{
          data: [27, 20, 16, 24, 11, 2],
          backgroundColor: ['#1e66ff','#20c997','#f59f00','#d6336c','#6f42c1','#adb5bd'],
          borderColor: '#fff',
          borderWidth: 3,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        cutout: '62%',
        plugins: {
          legend: {
            position: 'right',
            labels: { usePointStyle: true, pointStyle: 'circle', padding: 16 }
          }
        },
        layout: { padding: 8 }
      }
    });
  }

  function initTrafficChart(){
    var canvas = document.getElementById('trafficChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');

    new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct'],
        datasets: [
          {
            label: 'Visits',
            data: [120, 135, 150, 165, 180, 175, 190, 205, 198],
            borderColor: '#1e66ff',
            backgroundColor: 'rgba(30,102,255,0.15)',
            tension: 0.35,
            fill: true,
            pointRadius: 2
          },
          {
            label: 'Signups',
            data: [22, 28, 31, 36, 39, 41, 44, 47, 49],
            borderColor: '#20c997',
            backgroundColor: 'rgba(32,201,151,0.12)',
            tension: 0.35,
            fill: true,
            pointRadius: 2
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { position: 'bottom' } },
        scales: {
          x: { grid: { display: false } },
          y: { beginAtZero: true, grid: { color: 'rgba(0,0,0,0.06)' } }
        }
      }
    });
  }
})();

