(function(){
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init(){
    if (typeof Chart === 'undefined') return;
    initSalesChart();
    initBrowserUsageChart();
  }

  function initSalesChart(){
    var canvas = document.getElementById('salesChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');

    new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct'],
        datasets: [
          {
            label: 'This year',
            data: [36, 38, 58, 52, 60, 58, 61, 59, 63],
            backgroundColor: '#1e66ff',
            borderRadius: 3,
            categoryPercentage: 0.6,
            barPercentage: 0.75,
            maxBarThickness: 14
          },
          {
            label: 'Target',
            data: [30, 34, 32, 26, 36, 39, 40, 38, 36],
            backgroundColor: '#f59f00',
            borderRadius: 3,
            categoryPercentage: 0.6,
            barPercentage: 0.75,
            maxBarThickness: 14
          },
          {
            label: 'Last year',
            data: [68, 76, 92, 88, 84, 94, 89, 112, 90].map(v=>Math.round(v/2)),
            backgroundColor: '#20c997',
            borderRadius: 3,
            categoryPercentage: 0.6,
            barPercentage: 0.75,
            maxBarThickness: 14
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: true, position: 'bottom', labels: { usePointStyle: true, pointStyle: 'rectRounded' } },
          tooltip: { mode: 'index', intersect: false }
        },
        scales: {
          x: { grid: { display: false } },
          y: {
            beginAtZero: true,
            suggestedMax: 120,
            ticks: { stepSize: 30 },
            grid: { color: 'rgba(0,0,0,0.06)' }
          }
        }
      }
    });
  }

  function initBrowserUsageChart(){
    var canvas = document.getElementById('browserUsageChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Chrome','Edge','FireFox','Safari','Opera','IE'],
        datasets: [{
          data: [27, 20, 16, 24, 11, 2],
          backgroundColor: ['#1e66ff','#20c997','#f59f00','#d6336c','#6741d9','#adb5bd'],
          borderColor: '#fff',
          borderWidth: 2,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        cutout: '65%',
        plugins: {
          legend: {
            position: 'right',
            labels: { usePointStyle: true, pointStyle: 'circle' }
          },
          tooltip: {
            callbacks: {
              label: function(ctx){
                var label = ctx.label || '';
                var val = ctx.parsed;
                var total = ctx.chart._metasets[0].total || ctx.dataset.data.reduce(function(a,b){return a+b;},0);
                var pct = Math.round((val/total)*100);
                return label+': '+val+' ('+pct+'%)';
              }
            }
          }
        }
      }
    });
  }
})();

