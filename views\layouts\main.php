<?php

/** @var yii\web\View $this */
/** @var string $content */

use app\assets\AppAsset;
use app\assets\AdminLteAsset;
use app\widgets\Alert;
use yii\bootstrap5\Breadcrumbs;
use yii\bootstrap5\Html;
use yii\bootstrap5\Nav;
use yii\bootstrap5\NavBar;

AppAsset::register($this);
AdminLteAsset::register($this);
$this->registerCsrfMetaTags();
$this->registerMetaTag(['charset' => Yii::$app->charset], 'charset');
$this->registerMetaTag(['name' => 'viewport', 'content' => 'width=device-width, initial-scale=1, shrink-to-fit=no']);
$this->registerMetaTag(['name' => 'description', 'content' => $this->params['meta_description'] ?? '']);
$this->registerMetaTag(['name' => 'keywords', 'content' => $this->params['meta_keywords'] ?? '']);
$this->registerLinkTag(['rel' => 'icon', 'type' => 'image/x-icon', 'href' => Yii::getAlias('@web/favicon.ico')]);
?>
<?php $this->beginPage() ?>
<!DOCTYPE html>
<html lang="<?= Yii::$app->language ?>">

<head>
    <title><?= Html::encode($this->title) ?></title>
    <?php $this->head() ?>
</head>

<body class="sidebar-mini layout-fixed fixed-header fixed-footer layout-navbar-fixed layout-footer-fixed sidebar-expand-lg">
    <?php $this->beginBody() ?>

    <div class="app-wrapper">
        <?= $this->render('_header') ?>

        <?= $this->render('_sidebar') ?>

        <!-- Main -->
        <main class="app-main">
            <!-- Content Header -->
            <div class="app-content-header">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col">
                            <h3 class="mb-0"><?= Html::encode($this->title) ?></h3>
                        </div>
                        <div class="col-auto">
                            <?= Breadcrumbs::widget([
                                'links' => $this->params['breadcrumbs'] ?? [],
                                'options' => ['class' => 'breadcrumb mb-0'],
                            ]) ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Content -->
            <div class="app-content">
                <div class="container-fluid">
                    <?= Alert::widget() ?>
                    <?= $content ?>
                </div>
            </div>
        </main>

        <?= $this->render('_footer') ?>
    </div>


    <?php
    $this->registerJs(<<<JS
    document.addEventListener('click', function(e){
      const btn = e.target.closest('[data-lte-toggle="fullscreen"]');
      if(!btn) return;
      e.preventDefault();
      const doc = document;
      if (!doc.fullscreenElement && !doc.webkitFullscreenElement) {
        (doc.documentElement.requestFullscreen && doc.documentElement.requestFullscreen())
          || (doc.documentElement.webkitRequestFullscreen && doc.documentElement.webkitRequestFullscreen());
      } else {
        (doc.exitFullscreen && doc.exitFullscreen())
          || (doc.webkitExitFullscreen && doc.webkitExitFullscreen());
      }
    });
    JS);
    ?>


    <?php $this->endBody() ?>
</body>

</html>
<?php $this->endPage() ?>