{"name": "yiisoft/yii2-app-basic", "description": "Yii 2 Basic Project Template", "keywords": ["yii2", "framework", "basic", "project template"], "homepage": "https://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "https://www.yiiframework.com/forum/", "wiki": "https://www.yiiframework.com/wiki/", "irc": "ircs://irc.libera.chat:6697/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.4.0", "yiisoft/yii2": "~2.0.45", "yiisoft/yii2-bootstrap5": "~2.0.2", "yiisoft/yii2-symfonymailer": "~2.0.3", "fortawesome/font-awesome": "^7.1", "almasaeed2010/adminlte": "4.0.0-rc3", "npm-asset/bootstrap-icons": "1.11", "npm-asset/chart.js": "4.4"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "~2.2.0", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^5.0.0 || ^4.0", "codeception/lib-innerbrowser": "^4.0 || ^3.0 || ^1.1", "codeception/module-asserts": "^3.0 || ^1.1", "codeception/module-yii2": "^1.1", "codeception/module-filesystem": "^3.0 || ^2.0 || ^1.1", "codeception/verify": "^3.0 || ^2.2"}, "config": {"allow-plugins": {"yiisoft/yii2-composer": true}, "process-timeout": 1800, "fxp-asset": {"enabled": false}}, "scripts": {"post-install-cmd": ["yii\\composer\\Installer::postInstall"], "post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject", "yii\\composer\\Installer::postInstall"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}]}, "yii\\composer\\Installer::postInstall": {"generateCookieValidationKey": ["config/web.php"]}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}