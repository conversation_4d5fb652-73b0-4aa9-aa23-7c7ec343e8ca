/* Dashboard chart sizing */
.chart-box{ position: relative; height: 160px; }
.chart-canvas{ display:block; width:100% !important; height:100% !important; max-width:100%; }

.chart-box--donut{ height: 220px; }
.chart-box--sales{ height: 160px; }
.chart-box--wide{ height: 220px; }



main > .container {
    padding: 70px 15px 20px;
}

.footer {
    background-color: #f5f5f5;
    font-size: .9em;
    height: 60px;
}

.footer > .container {
    padding-right: 15px;
    padding-left: 15px;
}

.not-set {
    color: #c55;
    font-style: italic;
}

/* add sorting icons to gridview sort links */
a.asc:after, a.desc:after {
    content: '';
    left: 3px;
    display: inline-block;
    width: 0;
    height: 0;
    border: solid 5px transparent;
    margin: 4px 4px 2px 4px;
    background: transparent;
}

a.asc:after {
    border-bottom: solid 7px #212529;
    border-top-width: 0;
}

a.desc:after {
    border-top: solid 7px #212529;
    border-bottom-width: 0;
}

.grid-view th {
    white-space: nowrap;
}

.hint-block {
    display: block;
    margin-top: 5px;
    color: #999;
}

.error-summary {
    color: #a94442;
    background: #fdf7f7;
    border-left: 3px solid #eed3d7;
    padding: 10px 20px;
    margin: 0 0 15px 0;
}

/* align the logout "link" (button in form) of the navbar */
.nav li > form > button.logout {
    padding-top: 7px;
    color: rgba(255, 255, 255, 0.5);
}

@media(max-width:767px) {
    .nav li > form > button.logout {
        display:block;
        text-align: left;
        width: 100%;
        padding: 10px 0;
    }
}

.nav > li > form > button.logout:focus,
.nav > li > form > button.logout:hover {
    text-decoration: none;
    color: rgba(255, 255, 255, 0.75);
}

.nav > li > form > button.logout:focus {
    outline: none;
}

.form-group {
    margin-bottom: 1rem;
}

.sidebar {
    background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.sidebar .nav-link {
    border-radius: 5px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    background-color: #007bff;
    color: white !important;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: #007bff;
    color: white;
    box-shadow: 0 2px 5px rgba(0,123,255,0.3);
}

.main-content {
    background-color: #f8f9fa;
    min-height: 100vh;
}

body {
    overflow-x: hidden;
}
.sidebar {
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    transition: all 0.3s;
}
.sidebar .nav-link {
    color: #333;
    padding: 10px 15px;
    margin: 2px 0;
    border-radius: 5px;
}
.sidebar .nav-link:hover {
    background-color: #007bff;
    color: white;
}
.sidebar .nav-link.active {
    background-color: #007bff;
    color: white;
}

/* Mobile first - sidebar hidden by default */
.sidebar {
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    z-index: 999;
    background: white;
}

.main-content {
    margin-left: 0;
    width: 100%;
    transition: all 0.3s;
}

/* Desktop */
@media (min-width: 768px) {
    .sidebar {
        position: fixed;
        left: 0;
    }
    .main-content {
        margin-left: 250px;
    }
    .sidebar-toggle {
        display: none;
    }
}

/* When sidebar is open on mobile */
.sidebar-open .sidebar {
    left: 0;
}
.sidebar-open .main-content {
    transform: translateX(250px);
}

/* Overlay for mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 998;
}
.sidebar-open .sidebar-overlay {
    display: block;
}

/* AdminLTE 4: custom light-blue sidebar theme */
.app-sidebar.sidebar-lightblue {
    background-color: #e8f3ff !important; /* light blue */
    color: #0b3a67;
    border-right: 1px solid #cfe0f5;
}
.app-sidebar.sidebar-lightblue .sidebar-brand,
.app-sidebar.sidebar-lightblue .sidebar-wrapper {
    background-color: transparent !important;
}
.app-sidebar.sidebar-lightblue .nav-link {
    color: #0b3a67;
}
.app-sidebar.sidebar-lightblue .nav-link .nav-icon { color: inherit; }
.app-sidebar.sidebar-lightblue .nav-link:hover,
.app-sidebar.sidebar-lightblue .nav-link.active {
    background-color: #d7e9fb !important; /* slightly darker light blue */
    color: #0b3a67 !important;
}
.app-sidebar.sidebar-lightblue .nav-treeview .nav-link {
    color: #0f5ea8;
}


/* AdminLTE 4: ultra-light sidebar theme */
.app-sidebar.sidebar-ultralight {
  background: linear-gradient(180deg, #ffffff 0%, #f6fbff 100%) !important;
  color: #244d8a;
  border-right: 1px solid #e9eef6;
}
.app-sidebar.sidebar-ultralight .sidebar-brand,
.app-sidebar.sidebar-ultralight .sidebar-wrapper { background-color: transparent !important; }
.app-sidebar.sidebar-ultralight .nav-link { color: #244d8a; }
.app-sidebar.sidebar-ultralight .nav-link .nav-icon { color: #2f6db3; }
.app-sidebar.sidebar-ultralight .nav-link:hover {
  background-color: #eef5ff !important;
  color: #1d3f74 !important;
}
.app-sidebar.sidebar-ultralight .nav-link.active {
  background-color: #eef5ff !important;
  color: #1d3f74 !important;
  border-left: 3px solid #2b6cb0;
}
.app-sidebar.sidebar-ultralight .nav-treeview .nav-link {
  color: #3a7bd5;
}
/* Ensure Yii Menu active state (li.active) highlights the link */
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-link {
  background-color: #eef5ff !important;
  color: #1d3f74 !important;
  border-left: 3px solid #2b6cb0;
}
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-link .nav-icon {
  color: #2b6cb0;
}



/* Polish tweaks: emphasize active parents, rotate chevron, auto-expand */
.app-sidebar.sidebar-ultralight .nav .nav-item > .nav-link { transition: background-color .15s ease, color .15s ease; }
.app-sidebar.sidebar-ultralight .nav .nav-item > .nav-link .nav-arrow { transition: transform .2s ease; }

/* Make active links bolder and accent stronger */
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-link{ font-weight: 600; border-left-width: 4px; }

/* Rotate chevron for active parent (open state) */
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-link .nav-arrow { transform: rotate(90deg); }

/* Auto-show submenu when parent is active */
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-treeview { display: block; }

/* Slight emphasis for active item inside submenus */
.app-sidebar.sidebar-ultralight .nav-treeview .nav-link.active,
.app-sidebar.sidebar-ultralight .nav-treeview .nav-item.active > .nav-link{
  background-color:#f3f8ff!important;
  border-left: 2px solid #79a8e8;
}

/* Visual polish: rounded active items, subtle shadow, submenu slide */
.app-sidebar.sidebar-ultralight .nav .nav-item > .nav-link{ border-radius:.5rem; margin:0 .25rem; }
.app-sidebar.sidebar-ultralight .nav .nav-link:hover,
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-link{ box-shadow:0 1px 4px rgba(37,70,126,.08); }

/* Submenu animation (best-effort; AdminLTE may toggle display) */
.app-sidebar.sidebar-ultralight .nav .nav-item > .nav-treeview{ overflow:hidden; transition:max-height .2s ease; }
.app-sidebar.sidebar-ultralight .nav .nav-item.active > .nav-treeview{ max-height:600px; }


.captcha-group .captcha-image{ background:#fff; padding:2px; border:1px solid #e1e7ef; }
.captcha-group input.form-control{ max-width:240px; }
