/*!
 * AdminLTE v4.0.0-rc3 (https://adminlte.io)
 * Copyright 2014-2025 Colorlib <https://colorlib.com>
 * Licensed under MIT (https://github.com/ColorlibHQ/AdminLTE/blob/master/LICENSE)
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).adminlte={})}(this,function(e){"use strict";const t=[],n=e=>{"loading"===document.readyState?(t.length||document.addEventListener("DOMContentLoaded",()=>{for(const e of t)e()}),t.push(e)):e()},i=(e,t=500)=>{e.style.transitionProperty="height, margin, padding",e.style.transitionDuration=`${t}ms`,e.style.boxSizing="border-box",e.style.height=`${e.offsetHeight}px`,e.style.overflow="hidden",globalThis.setTimeout(()=>{e.style.height="0",e.style.paddingTop="0",e.style.paddingBottom="0",e.style.marginTop="0",e.style.marginBottom="0"},1),globalThis.setTimeout(()=>{e.style.display="none",e.style.removeProperty("height"),e.style.removeProperty("padding-top"),e.style.removeProperty("padding-bottom"),e.style.removeProperty("margin-top"),e.style.removeProperty("margin-bottom"),e.style.removeProperty("overflow"),e.style.removeProperty("transition-duration"),e.style.removeProperty("transition-property")},t)},o=(e,t=500)=>{e.style.removeProperty("display");let{display:n}=globalThis.getComputedStyle(e);"none"===n&&(n="block"),e.style.display=n;const i=e.offsetHeight;e.style.overflow="hidden",e.style.height="0",e.style.paddingTop="0",e.style.paddingBottom="0",e.style.marginTop="0",e.style.marginBottom="0",globalThis.setTimeout(()=>{e.style.boxSizing="border-box",e.style.transitionProperty="height, margin, padding",e.style.transitionDuration=`${t}ms`,e.style.height=`${i}px`,e.style.removeProperty("padding-top"),e.style.removeProperty("padding-bottom"),e.style.removeProperty("margin-top"),e.style.removeProperty("margin-bottom")},1),globalThis.setTimeout(()=>{e.style.removeProperty("height"),e.style.removeProperty("overflow"),e.style.removeProperty("transition-duration"),e.style.removeProperty("transition-property")},t)},s="hold-transition";class a{_element;constructor(e){this._element=e}holdTransition(){let e;window.addEventListener("resize",()=>{document.body.classList.add(s),clearTimeout(e),e=setTimeout(()=>{document.body.classList.remove(s)},400)})}}n(()=>{new a(document.body).holdTransition(),setTimeout(()=>{document.body.classList.add("app-loaded")},400)});const r=".lte.card-widget",l=`collapsed${r}`,c=`expanded${r}`,d=`remove${r}`,m=`maximized${r}`,u=`minimized${r}`,h="card",p="collapsed-card",g="collapsing-card",y="expanding-card",v="was-collapsed",b="maximized-card",f='[data-lte-toggle="card-remove"]',E='[data-lte-toggle="card-collapse"]',_='[data-lte-toggle="card-maximize"]',S=`.${h}`,L=".card-body",w=".card-footer",A={animationSpeed:500,collapseTrigger:E,removeTrigger:f,maximizeTrigger:_};class k{_element;_parent;_clone;_config;constructor(e,t){this._element=e,this._parent=e.closest(S),e.classList.contains(h)&&(this._parent=e),this._config={...A,...t}}collapse(){const e=new Event(l);if(this._parent){this._parent.classList.add(g);const e=this._parent?.querySelectorAll(`${L}, ${w}`);e.forEach(e=>{e instanceof HTMLElement&&i(e,this._config.animationSpeed)}),setTimeout(()=>{this._parent&&(this._parent.classList.add(p),this._parent.classList.remove(g))},this._config.animationSpeed)}this._element?.dispatchEvent(e)}expand(){const e=new Event(c);if(this._parent){this._parent.classList.add(y);const e=this._parent?.querySelectorAll(`${L}, ${w}`);e.forEach(e=>{e instanceof HTMLElement&&o(e,this._config.animationSpeed)}),setTimeout(()=>{this._parent&&this._parent.classList.remove(p,y)},this._config.animationSpeed)}this._element?.dispatchEvent(e)}remove(){const e=new Event(d);this._parent&&i(this._parent,this._config.animationSpeed),this._element?.dispatchEvent(e)}toggle(){this._parent?.classList.contains(p)?this.expand():this.collapse()}maximize(){const e=new Event(m);this._parent&&(this._parent.style.height=`${this._parent.offsetHeight}px`,this._parent.style.width=`${this._parent.offsetWidth}px`,this._parent.style.transition="all .15s",setTimeout(()=>{const e=document.querySelector("html");e&&e.classList.add(b),this._parent&&(this._parent.classList.add(b),this._parent.classList.contains(p)&&this._parent.classList.add(v))},150)),this._element?.dispatchEvent(e)}minimize(){const e=new Event(u);this._parent&&(this._parent.style.height="auto",this._parent.style.width="auto",this._parent.style.transition="all .15s",setTimeout(()=>{const e=document.querySelector("html");e&&e.classList.remove(b),this._parent&&(this._parent.classList.remove(b),this._parent?.classList.contains(v)&&this._parent.classList.remove(v))},10)),this._element?.dispatchEvent(e)}toggleMaximize(){this._parent?.classList.contains(b)?this.minimize():this.maximize()}}n(()=>{document.querySelectorAll(E).forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();const t=e.target;new k(t,A).toggle()})}),document.querySelectorAll(f).forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();const t=e.target;new k(t,A).remove()})}),document.querySelectorAll(_).forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();const t=e.target;new k(t,A).toggleMaximize()})})});const x=".lte.treeview",q=`expanded${x}`,T=`collapsed${x}`,$="menu-open",M=".nav-item",D=".nav-treeview",N={animationSpeed:300,accordion:!0};class P{_element;_config;constructor(e,t){this._element=e,this._config={...N,...t}}open(){const e=new Event(q);if(this._config.accordion){const e=this._element.parentElement?.querySelectorAll(`${M}.${$}`);e?.forEach(e=>{if(e!==this._element.parentElement){e.classList.remove($);const t=e?.querySelector(D);t&&i(t,this._config.animationSpeed)}})}this._element.classList.add($);const t=this._element?.querySelector(D);t&&o(t,this._config.animationSpeed),this._element.dispatchEvent(e)}close(){const e=new Event(T);this._element.classList.remove($);const t=this._element?.querySelector(D);t&&i(t,this._config.animationSpeed),this._element.dispatchEvent(e)}toggle(){this._element.classList.contains($)?this.close():this.open()}}n(()=>{document.querySelectorAll('[data-lte-toggle="treeview"]').forEach(e=>{e.addEventListener("click",e=>{const t=e.target,n=t.closest(M),i=t.closest(".nav-link"),o=e.currentTarget;if("#"!==t?.getAttribute("href")&&"#"!==i?.getAttribute("href")||e.preventDefault(),n){const e=o.dataset.accordion,t=o.dataset.animationSpeed,i={accordion:void 0===e?N.accordion:"true"===e,animationSpeed:void 0===t?N.animationSpeed:Number(t)};new P(n,i).toggle()}})})});const F=".lte.direct-chat",R=`expanded${F}`,C=`collapsed${F}`,z="direct-chat-contacts-open";class B{_element;constructor(e){this._element=e}toggle(){if(this._element.classList.contains(z)){const e=new Event(C);this._element.classList.remove(z),this._element.dispatchEvent(e)}else{const e=new Event(R);this._element.classList.add(z),this._element.dispatchEvent(e)}}}n(()=>{document.querySelectorAll('[data-lte-toggle="chat-pane"]').forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();const t=e.target.closest(".direct-chat");t&&new B(t).toggle()})})});const H=".lte.fullscreen",K=`maximized${H}`,O=`minimized${H}`,W='[data-lte-toggle="fullscreen"]',I='[data-lte-icon="maximize"]',j='[data-lte-icon="minimize"]';class U{_element;_config;constructor(e,t){this._element=e,this._config=t}inFullScreen(){const e=new Event(K),t=document.querySelector(I),n=document.querySelector(j);document.documentElement.requestFullscreen(),t&&(t.style.display="none"),n&&(n.style.display="block"),this._element.dispatchEvent(e)}outFullscreen(){const e=new Event(O),t=document.querySelector(I),n=document.querySelector(j);document.exitFullscreen(),t&&(t.style.display="block"),n&&(n.style.display="none"),this._element.dispatchEvent(e)}toggleFullScreen(){document.fullscreenEnabled&&(document.fullscreenElement?this.outFullscreen():this.inFullScreen())}}n(()=>{document.querySelectorAll(W).forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();const t=e.target.closest(W);t&&new U(t,void 0).toggleFullScreen()})})});const V=".lte.push-menu",G=`open${V}`,J=`collapse${V}`,Q="sidebar-mini",X="sidebar-collapse",Y="sidebar-open",Z="sidebar-expand",ee=`[class*="${Z}"]`,te='[data-lte-toggle="sidebar"]',ne={sidebarBreakpoint:992};class ie{_element;_config;constructor(e,t){this._element=e,this._config={...ne,...t}}menusClose(){document.querySelectorAll(".nav-treeview").forEach(e=>{e.style.removeProperty("display"),e.style.removeProperty("height")});const e=document.querySelector(".sidebar-menu"),t=e?.querySelectorAll(".nav-item");t&&t.forEach(e=>{e.classList.remove("menu-open")})}expand(){const e=new Event(G);document.body.classList.remove(X),document.body.classList.add(Y),this._element.dispatchEvent(e)}collapse(){const e=new Event(J);document.body.classList.remove(Y),document.body.classList.add(X),this._element.dispatchEvent(e)}addSidebarBreakPoint(){const e=document.querySelector(ee)?.classList??[],t=Array.from(e).find(e=>e.startsWith(Z))??"",n=document.getElementsByClassName(t)[0],i=globalThis.getComputedStyle(n,"::before").getPropertyValue("content");this._config={...this._config,sidebarBreakpoint:Number(i.replace(/[^\d.-]/g,""))},window.innerWidth<=this._config.sidebarBreakpoint?this.collapse():(document.body.classList.contains(Q)||this.expand(),document.body.classList.contains(Q)&&document.body.classList.contains(X)&&this.collapse())}toggle(){document.body.classList.contains(X)?this.expand():this.collapse()}init(){this.addSidebarBreakPoint()}}n(()=>{const e=document?.querySelector(".app-sidebar");if(e){const t=new ie(e,ne);t.init(),window.addEventListener("resize",()=>{t.init()})}const t=document.createElement("div");t.className="sidebar-overlay",document.querySelector(".app-wrapper")?.append(t),t.addEventListener("touchstart",e=>{e.preventDefault();const t=e.currentTarget;new ie(t,ne).collapse()},{passive:!0}),t.addEventListener("click",e=>{e.preventDefault();const t=e.currentTarget;new ie(t,ne).collapse()}),document.querySelectorAll(te).forEach(e=>{e.addEventListener("click",e=>{e.preventDefault();let t=e.currentTarget;"sidebar"!==t?.dataset.lteToggle&&(t=t?.closest(te)),t&&(e?.preventDefault(),new ie(t,ne).toggle())})})});class oe{config;liveRegion=null;focusHistory=[];constructor(e={}){this.config={announcements:!0,skipLinks:!0,focusManagement:!0,keyboardNavigation:!0,reducedMotion:!0,...e},this.init()}init(){this.config.announcements&&this.createLiveRegion(),this.config.skipLinks&&this.addSkipLinks(),this.config.focusManagement&&this.initFocusManagement(),this.config.keyboardNavigation&&this.initKeyboardNavigation(),this.config.reducedMotion&&this.respectReducedMotion(),this.initErrorAnnouncements(),this.initTableAccessibility(),this.initFormAccessibility()}createLiveRegion(){this.liveRegion||(this.liveRegion=document.createElement("div"),this.liveRegion.id="live-region",this.liveRegion.className="live-region",this.liveRegion.setAttribute("aria-live","polite"),this.liveRegion.setAttribute("aria-atomic","true"),this.liveRegion.setAttribute("role","status"),document.body.append(this.liveRegion))}addSkipLinks(){const e=document.createElement("div");e.className="skip-links";const t=document.createElement("a");t.href="#main",t.className="skip-link",t.textContent="Skip to main content";const n=document.createElement("a");n.href="#navigation",n.className="skip-link",n.textContent="Skip to navigation",e.append(t),e.append(n),document.body.insertBefore(e,document.body.firstChild),this.ensureSkipTargets()}ensureSkipTargets(){const e=document.querySelector('#main, main, [role="main"]');e&&!e.id&&(e.id="main"),e&&!e.hasAttribute("tabindex")&&e.setAttribute("tabindex","-1");const t=document.querySelector('#navigation, nav, [role="navigation"]');t&&!t.id&&(t.id="navigation"),t&&!t.hasAttribute("tabindex")&&t.setAttribute("tabindex","-1")}initFocusManagement(){document.addEventListener("keydown",e=>{"Tab"===e.key&&this.handleTabNavigation(e),"Escape"===e.key&&this.handleEscapeKey(e)}),this.initModalFocusManagement(),this.initDropdownFocusManagement()}handleTabNavigation(e){const t=this.getFocusableElements(),n=t.indexOf(document.activeElement);e.shiftKey?n<=0&&(e.preventDefault(),t.at(-1)?.focus()):n>=t.length-1&&(e.preventDefault(),t[0]?.focus())}getFocusableElements(){const e=["a[href]","button:not([disabled])","input:not([disabled])","select:not([disabled])","textarea:not([disabled])",'[tabindex]:not([tabindex="-1"])','[contenteditable="true"]'].join(", ");return Array.from(document.querySelectorAll(e))}handleEscapeKey(e){const t=document.querySelector(".modal.show"),n=document.querySelector(".dropdown-menu.show");if(t){const n=t.querySelector('[data-bs-dismiss="modal"]');n?.click(),e.preventDefault()}else if(n){const t=document.querySelector('[data-bs-toggle="dropdown"][aria-expanded="true"]');t?.click(),e.preventDefault()}}initKeyboardNavigation(){document.addEventListener("keydown",e=>{const t=e.target;t.closest(".nav, .navbar-nav, .dropdown-menu")&&this.handleMenuNavigation(e),"Enter"!==e.key&&" "!==e.key||!t.hasAttribute("role")||"button"!==t.getAttribute("role")||t.matches('button, input[type="button"], input[type="submit"]')||(e.preventDefault(),t.click())})}handleMenuNavigation(e){if(!["ArrowUp","ArrowDown","ArrowLeft","ArrowRight","Home","End"].includes(e.key))return;const t=e.target,n=Array.from(t.closest(".nav, .navbar-nav, .dropdown-menu")?.querySelectorAll("a, button")||[]),i=n.indexOf(t);let o;switch(e.key){case"ArrowDown":case"ArrowRight":o=i<n.length-1?i+1:0;break;case"ArrowUp":case"ArrowLeft":o=i>0?i-1:n.length-1;break;case"Home":o=0;break;case"End":o=n.length-1;break;default:return}e.preventDefault(),n[o]?.focus()}respectReducedMotion(){if(globalThis.matchMedia("(prefers-reduced-motion: reduce)").matches){document.body.classList.add("reduce-motion"),document.documentElement.style.scrollBehavior="auto";const e=document.createElement("style");e.textContent="\n        *, *::before, *::after {\n          animation-duration: 0.01ms !important;\n          animation-iteration-count: 1 !important;\n          transition-duration: 0.01ms !important;\n        }\n      ",document.head.append(e)}}initErrorAnnouncements(){new MutationObserver(e=>{e.forEach(e=>{e.addedNodes.forEach(e=>{if(e.nodeType===Node.ELEMENT_NODE){const t=e;t.matches(".alert-danger, .invalid-feedback, .error")&&this.announce(t.textContent||"Error occurred","assertive"),t.matches(".alert-success, .success")&&this.announce(t.textContent||"Success","polite")}})})}).observe(document.body,{childList:!0,subtree:!0})}initTableAccessibility(){document.querySelectorAll("table").forEach(e=>{if(e.hasAttribute("role")||e.setAttribute("role","table"),e.querySelectorAll("th").forEach(e=>{if(!e.hasAttribute("scope")){const t=e.closest("thead"),n=0===e.cellIndex;t?e.setAttribute("scope","col"):n&&e.setAttribute("scope","row")}}),!e.querySelector("caption")&&e.hasAttribute("title")){const t=document.createElement("caption");t.textContent=e.getAttribute("title")||"",e.insertBefore(t,e.firstChild)}})}initFormAccessibility(){document.querySelectorAll("input, select, textarea").forEach(e=>{const t=e;if(!t.labels?.length&&!t.hasAttribute("aria-label")&&!t.hasAttribute("aria-labelledby")){const e=t.getAttribute("placeholder");e&&t.setAttribute("aria-label",e)}if(t.hasAttribute("required")){const e=t.labels?.[0];if(e&&!e.querySelector(".required-indicator")){const t=document.createElement("span");t.className="required-indicator sr-only",t.textContent=" (required)",e.append(t)}}t.addEventListener("invalid",()=>{this.handleFormError(t)})})}handleFormError(e){const t=`${e.id||e.name}-error`;let n=document.getElementById(t);n||(n=document.createElement("div"),n.id=t,n.className="invalid-feedback",n.setAttribute("role","alert"),e.parentNode?.insertBefore(n,e.nextSibling)),n.textContent=e.validationMessage,e.setAttribute("aria-describedby",t),e.classList.add("is-invalid"),this.announce(`Error in ${e.labels?.[0]?.textContent||e.name}: ${e.validationMessage}`,"assertive")}initModalFocusManagement(){document.addEventListener("shown.bs.modal",e=>{const t=e.target.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');t.length>0&&t[0].focus(),this.focusHistory.push(document.activeElement)}),document.addEventListener("hidden.bs.modal",()=>{const e=this.focusHistory.pop();e&&e.focus()})}initDropdownFocusManagement(){document.addEventListener("shown.bs.dropdown",e=>{const t=e.target.querySelector(".dropdown-menu"),n=t?.querySelector("a, button");n&&n.focus()})}announce(e,t="polite"){this.liveRegion||this.createLiveRegion(),this.liveRegion&&(this.liveRegion.setAttribute("aria-live",t),this.liveRegion.textContent=e,setTimeout(()=>{this.liveRegion&&(this.liveRegion.textContent="")},1e3))}focusElement(e){const t=document.querySelector(e);t&&(t.focus(),t.scrollIntoView({behavior:"smooth",block:"center"}))}trapFocus(e){const t=e.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),n=Array.from(t),i=n[0],o=n.at(-1);e.addEventListener("keydown",e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===i&&(o?.focus(),e.preventDefault()):document.activeElement===o&&(i.focus(),e.preventDefault()))})}addLandmarks(){if(!document.querySelector("main")){const e=document.querySelector(".app-main");e&&(e.setAttribute("role","main"),e.id="main")}document.querySelectorAll(".navbar-nav, .nav").forEach((e,t)=>{e.hasAttribute("role")||e.setAttribute("role","navigation"),e.hasAttribute("aria-label")||e.setAttribute("aria-label",`Navigation ${t+1}`)});const e=document.querySelector('form[role="search"], .navbar-search');e&&!e.hasAttribute("role")&&e.setAttribute("role","search")}}const se=e=>new oe(e);n(()=>{new a(document.body).holdTransition(),se({announcements:!0,skipLinks:!0,focusManagement:!0,keyboardNavigation:!0,reducedMotion:!0}).addLandmarks(),setTimeout(()=>{document.body.classList.add("app-loaded")},400)}),e.CardWidget=k,e.DirectChat=B,e.FullScreen=U,e.Layout=a,e.PushMenu=ie,e.Treeview=P,e.initAccessibility=se});
//# sourceMappingURL=adminlte.min.js.map