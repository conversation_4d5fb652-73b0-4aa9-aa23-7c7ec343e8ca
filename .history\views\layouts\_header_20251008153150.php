<?php

use yii\bootstrap5\Html;
?>
<nav class="app-header navbar navbar-expand bg-body">
  <div class="container-fluid">
    <!-- Left -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-lte-toggle="sidebar" href="#" role="button" aria-label="Toggle sidebar">
          <i class="fas fa-bars"></i>
        </a>
      </li>
      <li class="nav-item d-none d-md-inline-block">
        <a href="<?= Yii::$app->homeUrl ?>" class="nav-link"><i class="fas fa-home"></i></a>
      </li>
    </ul>

    <!-- Right -->
    <ul class="navbar-nav ms-auto align-items-center">
      <!-- Fullscreen button -->
      <li class="nav-item">
        <a class="nav-link" href="#" data-lte-toggle="fullscreen" title="Fullscreen" aria-label="Toggle fullscreen">
          <i class="fas fa-expand"></i>
        </a>
      </li>

      <!-- User dropdown / login -->
      <?php if (!Yii::$app->user->isGuest): $username = Html::encode(Yii::$app->user->identity->username); ?>
        <li class="nav-item dropdown">
          <a class="nav-link d-flex align-items-center gap-2" data-bs-toggle="dropdown" href="#" role="button" aria-expanded="false">
            <i class="fas fa-user-circle fa-lg text-secondary"></i>
            <span class="d-none d-sm-inline"><?= $username ?></span>
          </a>
          <div class="dropdown-menu dropdown-menu-end shadow">
            <h6 class="dropdown-header">Signed in as <strong><?= $username ?></strong></h6>
            <div class="dropdown-divider"></div>
            <?= Html::a('<i class="fas fa-sign-out-alt me-2"></i>Logout', ['/site/logout'], [
              'class' => 'dropdown-item',
              'data' => ['method' => 'post'],
              'title' => 'Sign out'
            ]) ?>
          </div>
        </li>
      <?php else: ?>
        <li class="nav-item">
          <?= Html::a('<i class="fas fa-sign-in-alt me-2"></i>Login', ['/site/login'], ['class' => 'nav-link']) ?>
        </li>
      <?php endif; ?>

      <!-- Date on the far right -->
      <li class="nav-item d-none d-md-inline-block">
        <span class="nav-link text-muted">
          <?php
          $formatter = new IntlDateFormatter(
            'ro_RO',
            IntlDateFormatter::FULL,
            IntlDateFormatter::NONE,
            NULL,
            NULL,
            'EEEE, MMMM d, Y'
          );
          echo $formatter->format(new DateTime('2025-10-08'));
          ?>
        </span>
      </li>
    </ul>
  </div>
</nav>