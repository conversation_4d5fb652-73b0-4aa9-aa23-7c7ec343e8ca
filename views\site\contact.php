<?php

/** @var yii\web\View $this */
/** @var yii\bootstrap5\ActiveForm $form */
/** @var app\models\ContactForm $model */

use yii\bootstrap5\ActiveForm;
use yii\bootstrap5\Html;
use yii\captcha\Captcha;

$this->title = 'Contact';
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="site-contact">
    <h1><?= Html::encode($this->title) ?></h1>

    <?php if (Yii::$app->session->hasFlash('contactFormSubmitted')): ?>

        <div class="alert alert-success">
            Thank you for contacting us. We will respond to you as soon as possible.
        </div>

        <p>
            Note that if you turn on the Yii debugger, you should be able
            to view the mail message on the mail panel of the debugger.
            <?php if (Yii::$app->mailer->useFileTransport): ?>
                Because the application is in development mode, the email is not sent but saved as
                a file under <code><?= Yii::getAlias(Yii::$app->mailer->fileTransportPath) ?></code>.
                Please configure the <code>useFileTransport</code> property of the <code>mail</code>
                application component to be false to enable email sending.
            <?php endif; ?>
        </p>

    <?php else: ?>

        <p>
            If you have business inquiries or other questions, please fill out the following form to contact us.
            Thank you.
        </p>

        <div class="row">
            <div class="col-lg-5">

                <?php $form = ActiveForm::begin(['id' => 'contact-form']); ?>

                <?= $form->field($model, 'name')->textInput(['autofocus' => true]) ?>

                <?= $form->field($model, 'email') ?>

                <?= $form->field($model, 'subject') ?>

                <?= $form->field($model, 'body')->textarea(['rows' => 6]) ?>

                <?= $form->field($model, 'verifyCode')->widget(Captcha::class, [
                    'captchaAction' => 'site/captcha',
                    'template' => '<div class="captcha-group d-flex align-items-center gap-2">{image}<button type="button" class="btn btn-outline-secondary btn-sm" id="captcha-refresh" aria-label="Refresh security code"><i class="fas fa-sync-alt"></i></button>{input}</div>',
                    'imageOptions' => [
                        'id' => 'contact-captcha-image',
                        'class' => 'captcha-image rounded border',
                        'title' => 'Click to refresh',
                        'alt' => 'Captcha image',
                        'style' => 'height:46px'
                    ],
                    'options' => [
                        'class' => 'form-control',
                        'placeholder' => 'Enter the characters',
                        'autocomplete' => 'off',
                        'aria-label' => 'Verification code'
                    ],
                ]) ?>

                <div class="form-group">
                    <?= Html::submitButton('Submit', ['class' => 'btn btn-primary', 'name' => 'contact-button']) ?>
                </div>

                <?php ActiveForm::end(); ?>

            </div>
        </div>

    <?php endif; ?>
</div>

<div class="site-contact">
    <h1><?= Html::encode($this->title) ?></h1>

    <?php if (Yii::$app->session->hasFlash('contactFormSubmitted')): ?>

        <div class="alert alert-success">
            Thank you for contacting us. We will respond to you as soon as possible.
        </div>

        <p>
            Note that if you turn on the Yii debugger, you should be able
            to view the mail message on the mail panel of the debugger.
            <?php if (Yii::$app->mailer->useFileTransport): ?>
                Because the application is in development mode, the email is not sent but saved as
                a file under <code><?= Yii::getAlias(Yii::$app->mailer->fileTransportPath) ?></code>.
                Please configure the <code>useFileTransport</code> property of the <code>mail</code>
                application component to be false to enable email sending.
            <?php endif; ?>
        </p>

    <?php else: ?>

        <p>
            If you have business inquiries or other questions, please fill out the following form to contact us.
            Thank you.
        </p>

        <div class="row">
            <div class="col-lg-5">

                <?php $form = ActiveForm::begin(['id' => 'contact-form']); ?>

                <?= $form->field($model, 'name')->textInput(['autofocus' => true]) ?>

                <?= $form->field($model, 'email') ?>

                <?= $form->field($model, 'subject') ?>

                <?= $form->field($model, 'body')->textarea(['rows' => 6]) ?>

                <?= $form->field($model, 'verifyCode')->widget(Captcha::class, [
                    'captchaAction' => 'site/captcha',
                    'template' => '<div class="captcha-group d-flex align-items-center gap-2">{image}<button type="button" class="btn btn-outline-secondary btn-sm" id="captcha-refresh-2" aria-label="Refresh security code"><i class="fas fa-sync-alt"></i></button>{input}</div>',
                    'imageOptions' => [
                        'id' => 'contact-captcha-image-2',
                        'class' => 'captcha-image rounded border',
                        'title' => 'Click to refresh',
                        'alt' => 'Captcha image',
                        'style' => 'height:46px'
                    ],
                    'options' => [
                        'class' => 'form-control',
                        'placeholder' => 'Enter the characters',
                        'autocomplete' => 'off',
                        'aria-label' => 'Verification code'
                    ],
                ]) ?>
<?php
$this->registerJs(<<<JS
  document.addEventListener('click', function(e){
    if(e.target.closest('#captcha-refresh')){
      $('#contact-captcha-image').yiiCaptcha('refresh');
    }
    if(e.target.closest('#captcha-refresh-2')){
      $('#contact-captcha-image-2').yiiCaptcha('refresh');
    }
  });
JS);
?>


                <div class="form-group">
                    <?= Html::submitButton('Submit', ['class' => 'btn btn-primary', 'name' => 'contact-button']) ?>
                </div>

                <?php ActiveForm::end(); ?>

            </div>
        </div>

    <?php endif; ?>
</div>