<?php
use yii\bootstrap5\Html;
use yii\widgets\Menu;
?>
<aside class="app-sidebar sidebar-ultralight shadow" data-bs-theme="light">
  <div class="sidebar-brand">
    <a href="<?= Yii::$app->homeUrl ?>" class="brand-link">
      <span class="brand-text fw-light"><?= Html::encode(Yii::$app->name) ?></span>
    </a>
  </div>
  <div class="sidebar-wrapper">
    <nav class="mt-2">
      <?= Menu::widget([
        'options' => [
          'class' => 'nav sidebar-menu flex-column',
          'role' => 'menu',
          'data-lte-toggle' => 'treeview',
          'data-accordion' => 'false',
        ],
        'itemOptions' => ['class' => 'nav-item'],
        'linkTemplate' => '<a href="{url}" class="nav-link">{label}</a>',
        'submenuTemplate' => "\n<ul class=\"nav nav-treeview\">\n{items}\n</ul>\n",
        'encodeLabels' => false,
        'activateParents' => true,
        'items' => [
          [
            'label' => '<i class="nav-icon fas fa-tachometer-alt"></i><p>Dashboard</p>',
            'url' => ['/site/index'],
          ],
          [
            'label' => '<i class="nav-icon fas fa-user"></i><p>Users</p>',
            'url' => ['/user/index'],
          ],
          [
            'label' => '<i class="nav-icon fas fa-cog"></i><p>Settings<i class="nav-arrow bi bi-chevron-right"></i></p>',
            'url' => '#',
            'items' => [
              [
                'label' => '<i class="nav-icon far fa-circle"></i><p>Basic Settings</p>',
                'url' => ['/site/settings', 'tab' => 'basic'],
              ],
              [
                'label' => '<i class="nav-icon far fa-circle"></i><p>Advanced Settings</p>',
                'url' => ['/site/settings', 'tab' => 'advanced'],
              ],
            ],
          ],
          [
            'label' => '<i class="nav-icon fas fa-info-circle"></i><p>About</p>',
            'url' => ['/site/about'],
          ],
          [
            'label' => '<i class="nav-icon fas fa-envelope"></i><p>Contact</p>',
            'url' => ['/site/contact'],
          ],
          Yii::$app->user->isGuest
            ? [
              'label' => '<i class="nav-icon fas fa-sign-in-alt"></i><p>Login</p>',
              'url' => ['/site/login'],
            ]
            : [
              'label' => '<i class="nav-icon fas fa-sign-out-alt"></i><p>Logout (' . Html::encode(Yii::$app->user->identity->username) . ')</p>',
              'url' => ['/site/logout'],
              'template' => '<a href="{url}" class="nav-link" data-method="post">{label}</a>',
            ],
        ],
      ]) ?>
    </nav>
  </div>
</aside>

