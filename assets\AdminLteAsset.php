<?php
namespace app\assets;

use yii\web\AssetBundle;

class AdminLteAsset extends AssetBundle
{
    public $sourcePath = '@vendor/almasaeed2010/adminlte';

    public $css = [
        'dist/css/adminlte.min.css',
    ];

    public $js = [
        'dist/js/adminlte.min.js',
        // Chart.js removed CDN. If charts are needed, we can install a local package and add it here.
    ];

    public $depends = [
        'yii\\web\\YiiAsset',
        'app\\assets\\ChartJsAsset', // provide Chart.js locally (optional, no-op if unused)
        'yii\\bootstrap5\\BootstrapAsset',
        'yii\\bootstrap5\\BootstrapPluginAsset',
    ];
}

