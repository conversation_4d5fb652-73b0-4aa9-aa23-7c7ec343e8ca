(function(){
  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  function init(){
    if (typeof Chart === 'undefined') return;
    initSalesChart();
    initBrowserUsageChart();
  }

  function initSalesChart(){
    var canvas = document.getElementById('salesChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'],
        datasets: [{
          label: 'Sales',
          data: [12, 19, 3, 5, 2, 3, 9, 14, 11, 17, 21, 25],
          borderColor: 'rgba(54, 162, 235, 1)',
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          tension: 0.3,
          fill: true,
          pointRadius: 3
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: { legend: { display: true } },
        scales: {
          x: { grid: { display: false } },
          y: { beginAtZero: true }
        }
      }
    });
  }

  function initBrowserUsageChart(){
    var canvas = document.getElementById('browserUsageChart');
    if (!canvas) return;
    var ctx = canvas.getContext('2d');
    new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['Chrome','Edge','FireFox','Safari','Opera','IE'],
        datasets: [{
          data: [27, 20, 16, 24, 11, 2],
          backgroundColor: ['#1e66ff','#20c997','#f59f00','#d6336c','#6741d9','#adb5bd'],
          borderColor: '#fff',
          borderWidth: 2,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        cutout: '65%',
        plugins: {
          legend: {
            position: 'right',
            labels: { usePointStyle: true, pointStyle: 'circle' }
          },
          tooltip: {
            callbacks: {
              label: function(ctx){
                var label = ctx.label || '';
                var val = ctx.parsed;
                var total = ctx.chart._metasets[0].total || ctx.dataset.data.reduce(function(a,b){return a+b;},0);
                var pct = Math.round((val/total)*100);
                return label+': '+val+' ('+pct+'%)';
              }
            }
          }
        }
      }
    });
  }
})();

